import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:file_picker/file_picker.dart';
import '../../shared/components/app_textfield.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class AgentRegistrationScreen extends HookWidget {
  AgentRegistrationScreen({super.key});

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController agentLicenseIdController =
      TextEditingController();
  final TextEditingController additionalInfoController =
      TextEditingController();
  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  // File variable for agent license document upload
  final ValueNotifier<PlatformFile?> agentLicenseFile = ValueNotifier(null);
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);

    return _formWidget(context, size, isMobile);
  }

  Widget _formWidget(BuildContext context, Size size, bool isMobile) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(color: AppTheme.scaffoldBgColor),
          child: Center(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: isMobile ? double.infinity : 600,
              ),
              margin: EdgeInsets.symmetric(
                horizontal: isMobile ? defaultPadding : defaultPadding * 2,
                vertical: defaultPadding,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _formHeader(context),
                  _formContent(isMobile, context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 1.5,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            AppStrings.registerAgent,
            style: AppFonts.semiBoldTextStyle(20, color: Colors.white),
          ),
          const SizedBox(height: defaultPadding * 1.5),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: Colors.white,
            ),
            padding: const EdgeInsets.all(4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _registerTab(
                  context,
                  AppStrings.register,
                  selectedIndex.value == 0,
                  0,
                ),
                _registerTab(
                  context,
                  AppStrings.inviteAgent,
                  selectedIndex.value == 1,
                  1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _registerTab(
    BuildContext context,
    String label,
    bool isActive,
    int index,
  ) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.all(2),
        child: AppButton(
          label: label,
          backgroundColor: isActive ? AppTheme.roundIconColor : Colors.white,
          foregroundColor: Colors.white,
          elevation: 0,
          borderSide: BorderSide.none,
          borderRadius: 50,
          padding: EdgeInsets.symmetric(vertical: 12),
          textStyle: AppFonts.mediumTextStyle(
            14,
            color: isActive ? Colors.white : AppTheme.roundIconColor,
          ),
          onPressed: () {
            _clearForm();
            selectedIndex.value = index;
          },
        ),
      ),
    );
  }

  Container _formContent(bool isMobile, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                AppStrings.agentInformation,
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 1.5),

            _buildFormFields(isMobile),

            SizedBox(height: defaultPadding * 2),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields(bool isMobile) {
    bool isRegisterTab = selectedIndex.value == 0;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormLabel(AppStrings.firstName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            firstNameController,
            AppStrings.enterFirstName,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.lastName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            lastNameController,
            AppStrings.enterLastName,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(),
          const SizedBox(height: defaultPadding),

          if (isRegisterTab) ..._buildRegisterFields(true),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.firstName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      firstNameController,
                      AppStrings.enterFirstName,
                      isMandatory: true,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.lastName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      lastNameController,
                      AppStrings.enterLastName,
                      isMandatory: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(),
          const SizedBox(height: defaultPadding),

          if (isRegisterTab) ..._buildRegisterFields(false),
        ],
      );
    }
  }

  List<Widget> _buildRegisterFields(bool isMobile) {
    if (isMobile) {
      return [
        _buildFormLabel(AppStrings.city),
        const SizedBox(height: 8),
        _buildTextFormField(cityController, ''),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.stateProvince),
        const SizedBox(height: 8),
        _buildTextFormField(stateController, ''),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.postalZipCode),
        const SizedBox(height: 8),
        _buildTextFormField(postalCodeController, ''),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.country),
        const SizedBox(height: 8),
        _buildTextFormField(countryController, ''),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          agentLicenseIdController,
          AppStrings.enterAgentLicenseId,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.uploadAgentLicenseId),
        const SizedBox(height: 8),
        _buildUploadField(
          AppStrings.chooseFileOrDragDrop,
          AppStrings.pdfOrImageOnly,
          agentLicenseFile,
          ['pdf', 'jpg', 'png', 'jpeg'],
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(additionalInfoController, '', maxLines: 4),
      ];
    } else {
      return [
        _textFormFieldRowWidget(
          AppStrings.city,
          cityController,
          AppStrings.stateProvince,
          stateController,
        ),
        const SizedBox(height: defaultPadding),

        _textFormFieldRowWidget(
          AppStrings.postalZipCode,
          postalCodeController,
          AppStrings.country,
          countryController,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          agentLicenseIdController,
          AppStrings.enterAgentLicenseId,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.uploadAgentLicenseId),
        const SizedBox(height: 8),
        _buildUploadField(
          AppStrings.chooseFileOrDragDrop,
          AppStrings.pdfOrImageOnly,
          agentLicenseFile,
          ['pdf', 'jpg', 'png', 'jpeg'],
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(additionalInfoController, '', maxLines: 4),
      ];
    }
  }

  Widget _textFormFieldRowWidget(
    String leftLabel,
    TextEditingController leftController,
    String rightLabel,
    TextEditingController rightController,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(leftLabel),
              const SizedBox(height: 8),
              _buildTextFormField(leftController, ''),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(rightLabel),
              const SizedBox(height: 8),
              _buildTextFormField(rightController, ''),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        AppButton(
          label: AppStrings.clear,
          backgroundColor: AppTheme.scaffoldBgColor,
          foregroundColor: AppTheme.primaryTextColor,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2.5,
            vertical: defaultPadding / 2,
          ),
          onPressed: () => _showClearDataAlert(context),
        ),
        const SizedBox(width: defaultPadding),
        AppButton(
          label: AppStrings.register,
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding / 2,
          ),
          onPressed: () => _submitForm(context),
        ),
      ],
    );
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
  }) {
    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      validator: validator,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      isMobile: false,
    );
  }

  Widget _emailTextFormField() {
    return _buildTextFormField(
      emailController,
      AppStrings.enterEmail,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
    );
  }

  Widget _phoneNumTextFormField() {
    return _buildTextFormField(
      phoneController,
      AppStrings.enterPhone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      validator: (value) => InputValidators.validatePhone(value),
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) {
    return ValueListenableBuilder<PlatformFile?>(
      valueListenable: fileNotifier,
      builder: (context, file, child) {
        final isSmallMobile = Responsive.isSmallMobile(context);
        return buildDottedBorderContainerWithRadius(
          borderRadius: 25.0,
          child: Container(
            width: double.infinity,
            height: isSmallMobile ? 100 : 120,
            padding: EdgeInsets.all(
              isSmallMobile ? defaultPadding / 2 : defaultPadding,
            ),
            decoration: BoxDecoration(
              color: file != null
                  ? Colors.green.shade50
                  : AppTheme.docUploadBgColor,
              borderRadius: BorderRadius.circular(25),
              border: file != null
                  ? Border.all(color: Colors.green.shade200)
                  : null,
            ),
            child: file != null
                ? Stack(
                    children: [
                      Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green,
                              size: isSmallMobile ? 16 : 20,
                            ),
                            SizedBox(width: isSmallMobile ? 8 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    file['name'] ?? 'Selected file',
                                    style: AppFonts.mediumTextStyle(
                                      isSmallMobile ? 12 : 14,
                                      color: Colors.green.shade700,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    file['size'] != null
                                        ? '${(file['size'] / 1024).toStringAsFixed(1)} KB'
                                        : 'File selected',
                                    style: AppFonts.regularTextStyle(
                                      isSmallMobile ? 10 : 12,
                                      color: Colors.green.shade600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () => fileNotifier.value = null,
                          child: Icon(
                            Icons.close,
                            color: Colors.red,
                            size: isSmallMobile ? 16 : 20,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () =>
                            _pickFile(fileNotifier, allowedExtensions),
                        icon: Image.asset(
                          '$iconAssetpath/upload.png',
                          height: isSmallMobile ? 14 : 16,
                          width: isSmallMobile ? 14 : 16,
                        ),
                        label: Text(
                          AppStrings.upload,
                          style: AppFonts.mediumTextStyle(
                            isSmallMobile ? 12 : 14,
                            color: AppTheme.black,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppTheme.primaryTextColor,
                          elevation: 0,
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallMobile ? 8 : 12,
                            vertical: isSmallMobile ? 4 : 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: AppTheme.borderColor),
                          ),
                        ),
                      ),
                      SizedBox(height: isSmallMobile ? 4 : 8),
                      Text(
                        hintText,
                        textAlign: TextAlign.center,
                        style: AppFonts.mediumTextStyle(
                          isSmallMobile ? 10 : 12,
                          color: AppTheme.black,
                        ),
                      ),
                      Text(
                        formatText,
                        textAlign: TextAlign.center,
                        style: AppFonts.regularTextStyle(
                          isSmallMobile ? 9 : 12,
                          color: AppTheme.ternaryTextColor,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  Future<void> _pickFile(
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        fileNotifier.value = result.files.first;
      }
    } catch (e) {
      // Handle error
      print('Error picking file: $e');
    }
  }

  _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
    agentLicenseIdController.clear();
    additionalInfoController.clear();
    agentLicenseFile.value = null;
  }

  _submitForm(BuildContext context) {
    if (_formKey.currentState!.validate()) {
      // Form is valid, proceed with submission
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text(AppStrings.processingData)));
      // TODO: Add your submission logic here
    } else {
      // Form is invalid, show error message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppStrings.pleaseFillRequiredFields)),
      );
    }
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(AppStrings.clearData),
          content: Text(
            AppStrings.clearDataConfirmation,
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(AppStrings.cancel),
            ),
            TextButton(
              onPressed: () {
                _clearForm();
                Navigator.pop(context);
              },
              child: Text(AppStrings.ok),
            ),
          ],
        );
      },
    );
  }
}
